{"name": "cps_registration_portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development --base=/oauthregistrationportal", "prod": "vite --mode production --base=/oauthregistrationportal", "build": "tsc && vite build --base=/oauthregistrationportal", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --base=/oauthregistrationportal"}, "dependencies": {"@hookform/resolvers": "^3.4.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@tanstack/react-query": "^5.40.0", "@tanstack/react-table": "^8.17.3", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.381.0", "next-themes": "^0.3.0", "oidc-react": "^1.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.51.5", "react-router-dom": "^6.23.1", "sonner": "^1.4.41", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.12.13", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.2.2", "vite": "^5.2.0"}}