import { MessageError } from '@/types/MessageError'
import { AxiosError } from 'axios'

export const getAPIErrors = (error: AxiosError<MessageError>) => {
  const domainValidationErrors = getDomainValidationErrors(error)
  const messageErrors = getMessageErrors(error)

  if (domainValidationErrors) {
    return domainValidationErrors
  }

  if (messageErrors) {
    return messageErrors
  }

  return error.message || 'An error occurred'
}

const getDomainValidationErrors = (error: AxiosError<MessageError>) => {
  const domainValidations = error.response?.data?.domainValidations
  return extractErrorMessage(domainValidations)
}

const getMessageErrors = (error: AxiosError<MessageError>) => {
  const messages = error.response?.data?.messages
  return extractErrorMessage(messages)
}

const extractErrorMessage = (
  errorData: Record<string, string[]> | undefined
) => {
  if (!errorData) {
    return ''
  }

  let errorMessage = ''
  Object.values(errorData).forEach((value) => {
    errorMessage += value
  })
  return errorMessage
}
