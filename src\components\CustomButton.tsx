import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'

const sizeButtons = {
  xsmall: 'h-8 text-xs',
  small: 'px-2 py-2 text-sm',
  medium: 'px-4 py-4 text-base',
  large: 'px-6 py-6 text-lg',
}

interface CustomButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  size: 'xsmall' | 'small' | 'medium' | 'large'
  children: React.ReactNode
  isLoading?: boolean
  className?: string
  variant: 'destructive' | 'outline' | 'link' | 'ghost' | 'default'
}

const CustomButton = ({
  size = 'medium',
  children,
  isLoading = false,
  className = '',
  variant = 'default',
  ...props
}: CustomButtonProps) => {
  const sizeClass = sizeButtons[size] || sizeButtons.medium

  return (
    <Button
      variant={variant}
      disabled={isLoading}
      className={`${sizeClass} ${className}`}
      {...props}
    >
      {isLoading && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
      {children}
    </Button>
  )
}

export default CustomButton
