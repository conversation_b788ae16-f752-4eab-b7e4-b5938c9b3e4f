import { UserManager } from 'oidc-react'
import { ROUTE } from './route'
import { envConfigs } from './envUtils'

const originOnly = window.location.origin
const origin = window.location.origin + import.meta.env.VITE_WEB_PATH_BASE

const userManagerConfig = {
  // authority: import.meta.env.VITE_AUTHORITY_BASE,
  authority: envConfigs.IdentityApiUrl,
  client_id: envConfigs.ClientId,
  scope: 'openid profile thirdparty',
  response_type: 'id_token token',
  redirect_uri: `${origin}/callback`,
  post_logout_redirect_uri: `${origin}/`,
  extraQueryParams: {
    resource_register: `${originOnly}${ROUTE.REGISTER}`,
    resource_forgot_password: `${originOnly}${ROUTE.FORGOT_PASSWORD}`,
  },
}

const userManager = new UserManager(userManagerConfig)

export default userManager
