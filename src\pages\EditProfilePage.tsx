import { Loader2 } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { Link, useNavigate } from 'react-router-dom'
import { z } from 'zod'

import { useUpdateProfile } from '@/api/api'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import useCustomToast from '@/hooks/useCustomToast'
import { ROUTE } from '@/utils/route'
import { getUserData, updateUserData } from '@/utils/userUtils'
import { zodResolver } from '@hookform/resolvers/zod'

const formSchema = z.object({
  firstName: z
    .string()
    .min(1, { message: 'First Name is required' })
    .max(50, { message: 'First Name is too long' }),
  lastName: z
    .string()
    .min(1, { message: 'Last Name is required' })
    .max(50, { message: 'Last Name is too long' }),
  email: z
    .string()
    .email({ message: 'Invalid email address' })
    .max(150, { message: 'Email is too long' }),
})

const EditProfilePage = () => {
  const user = getUserData()
  const toast = useCustomToast()
  const navigate = useNavigate()
  const { mutateAsync, isPending } = useUpdateProfile()
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: user?.profile?.first_name,
      lastName: user?.profile?.last_name,
      email: user?.profile?.email,
    },
  })

  const submit = async (data: z.infer<typeof formSchema>) => {
    await mutateAsync(data, {
      onSuccess: () => {
        toast({ title: 'Profile updated' })
        updateUserData(data.firstName, data.lastName, data.email)
        navigate(ROUTE.CLIENTS)
      },
    })
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(submit)}>
          <Card className='w-full max-w-xl mx-auto'>
            <CardHeader>
              <CardTitle className='header'>Edit Profile</CardTitle>
            </CardHeader>
            <CardContent className='grid md:grid-cols-2 gap-4 text-left'>
              <FormField
                control={form.control}
                name='firstName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder='First Name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='lastName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder='Last Name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder='Email' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <div className='w-full flex gap-4 lg:justify-end'>
                <Link to={ROUTE.CLIENTS} className='w-full lg:w-auto'>
                  <Button
                    type='submit'
                    className='w-full lg:w-auto'
                    variant='outline'
                  >
                    Cancel
                  </Button>
                </Link>
                <Button className='w-full lg:w-auto' disabled={isPending}>
                  {isPending && (
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  )}
                  Save
                </Button>
              </div>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  )
}

export default EditProfilePage
