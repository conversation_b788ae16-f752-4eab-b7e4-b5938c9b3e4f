import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { Link } from 'react-router-dom'
import { z } from 'zod'

import { useLoginClient } from '@/api/api'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ROUTE } from '@/utils/route'

const formSchema = z.object({
  email: z
    .string({ required_error: 'Email is required' })
    .min(1, { message: 'Email is required' })
    .email({ message: 'Invalid email address' }),
  password: z
    .string({ required_error: 'Password is required' })
    .min(1, { message: 'Password is required' }),
})

const LoginPage = () => {
  const { mutateAsync } = useLoginClient()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const submit = async (data: z.infer<typeof formSchema>) => {
    await mutateAsync(data, {
      onSuccess: () => {},
    })
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(submit)}>
          <Card className='w-full max-w-sm mx-auto'>
            <CardHeader>
              <CardTitle className='header'>Login</CardTitle>
            </CardHeader>
            <CardContent className='grid gap-4 text-left'>
              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder='Email' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='password'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Password'
                        {...field}
                        type='password'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <div className='w-full flex flex-col gap-4'>
                <Button className='w-full' type='submit'>
                  Sign in
                </Button>
                <div className='grid sm:grid-cols-2 gap-2'>
                  <Link to={ROUTE.REGISTER}>
                    <Button
                      variant='secondary'
                      className='w-full'
                      type='button'
                    >
                      Register Account
                    </Button>
                  </Link>
                  <Button variant='secondary' className='w-full' type='button'>
                    Forgot Password
                  </Button>
                </div>
              </div>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  )
}

export default LoginPage
