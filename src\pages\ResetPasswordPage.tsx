import { Loader2 } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { Link, useLocation } from 'react-router-dom'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from '@/components/ui/card'
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Form,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ROUTE } from '@/utils/route'
import userManager from '@/utils/userManager'
import { useUpdatePassword } from '@/api/api'
import { Separator } from '@/components/ui/separator'
import useCustomToast from '@/hooks/useCustomToast'
import { useState } from 'react'
import CustomSpinner from '@/components/CustomSpinner'
import {
  INVALID_PASSWORD_COMPLEXITY_MSG,
  LOWER_CASE_REGEX,
  MIN_LENGTH_PASSWORD,
  NUMBER_REGEX,
  SPECIAL_CHAR_REGEX,
  UPPER_CASE_REGEX,
} from '@/utils/constants/constants'

const formSchema = z
  .object({
    password: z
      .string({ required_error: 'Password is required' })
      .min(MIN_LENGTH_PASSWORD, {
        message: `Password length should be between ${MIN_LENGTH_PASSWORD} and 50 chars.`,
      })
      .max(50, {
        message: `Password length should be between ${MIN_LENGTH_PASSWORD} and 50 chars.`,
      }),
    confirmPassword: z
      .string({ required_error: 'Confirm Password is required' })
      .min(MIN_LENGTH_PASSWORD, {
        message: `Confirm Password length should be between ${MIN_LENGTH_PASSWORD} and 50 chars.`,
      })
      .max(50, {
        message: `Confirm Password length should be between ${MIN_LENGTH_PASSWORD} and 50 chars.`,
      }),
  })
  .refine(
    (data: { password: string }) => {
      const hasUpperCase = UPPER_CASE_REGEX.test(data.password)
      const hasLowerCase = LOWER_CASE_REGEX.test(data.password)
      const hasNumber = NUMBER_REGEX.test(data.password)
      const hasSymbol = SPECIAL_CHAR_REGEX.test(data.password)

      const complexityCount = [
        hasUpperCase,
        hasLowerCase,
        hasNumber,
        hasSymbol,
      ].filter(Boolean).length

      return complexityCount >= 3
    },
    {
      message: INVALID_PASSWORD_COMPLEXITY_MSG,
      path: ['password'],
    }
  )
  .refine(
    (data: { password: string; confirmPassword: string }) =>
      data.password === data.confirmPassword,
    {
      message: 'Passwords do not match',
      path: ['confirmPassword'],
    }
  )

const ResetPasswordPage = () => {
  const toast = useCustomToast()
  const { mutateAsync, isPending } = useUpdatePassword()
  const location = useLocation()
  const queryParams = new URLSearchParams(location.search)
  const queryEmailKey = queryParams.get('email') || ''
  const [isRedirecting, setIsRedirecting] = useState<boolean>(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const submit = async (data: z.infer<typeof formSchema>) => {
    await mutateAsync(
      {
        newPassword: data.password,
        key: queryParams.get('key') ?? '',
        email: queryEmailKey,
        timeStamp: queryParams.get('timeStamp') ?? '',
      },
      {
        onSuccess: () => {
          setIsRedirecting(true)
          toast({
            title: 'Password reset successfully',
          })
          userManager.signinRedirect()
        },
      }
    )
  }

  if (isRedirecting) {
    return <CustomSpinner label='Redirecting...' />
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(submit)}>
          <Card className='w-full max-w-sm mx-auto'>
            <CardHeader>
              <CardTitle className='header'>Reset Password</CardTitle>
            </CardHeader>
            <CardContent className='grid gap-4 text-left'>
              <FormItem>
                <FormLabel>Account Information</FormLabel>
                <FormControl>
                  <Input
                    disabled
                    placeholder='<EMAIL>'
                    type='email'
                    value={queryEmailKey}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
              <Separator className='my-4' />
              <FormField
                control={form.control}
                name='password'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Password'
                        {...field}
                        type='password'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='confirmPassword'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Confirm Password'
                        {...field}
                        type='password'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <div className='w-full flex gap-4 lg:justify-end'>
                <Link to={ROUTE.WELCOME} className='w-full lg:w-auto'>
                  <Button
                    type='submit'
                    className='w-full lg:w-auto'
                    variant='outline'
                  >
                    Cancel
                  </Button>
                </Link>
                <Button className='w-full lg:w-auto' disabled={isPending}>
                  {isPending && (
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  )}
                  Save
                </Button>
              </div>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  )
}

export default ResetPasswordPage
