import { Search } from 'lucide-react'

import { Input } from './ui/input'

interface CustomSearchInputProps {
  onSearchTextChange: (event: React.ChangeEvent<HTMLInputElement>) => void
}

const CustomSearchInput = ({ onSearchTextChange }: CustomSearchInputProps) => {
  return (
    <>
      <div className='relative'>
        <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
        <Input
          type='search'
          placeholder='Search...'
          className='rounded-lg bg-background pl-8 md:w-[336px]'
          onChange={onSearchTextChange}
        />
      </div>
    </>
  )
}

export default CustomSearchInput
