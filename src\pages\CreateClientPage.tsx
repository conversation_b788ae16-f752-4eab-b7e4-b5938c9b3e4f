import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link, useNavigate } from 'react-router-dom'
import { ROUTE } from '../utils/route'
import { Textarea } from '@/components/ui/textarea'
import { useCreateClient } from '@/api/api'
import useCustomToast from '@/hooks/useCustomToast'
import { Loader2 } from 'lucide-react'

const formSchema = z.object({
  clientName: z
    .string({ required_error: 'Name is required' })
    .min(1, { message: 'Name is required' }),
  description: z
    .string({ required_error: 'Description is required' })
    .min(1, { message: 'Description is required' }),
  clientUri: z
    .string({ required_error: 'URI is required' })
    .min(1, { message: 'URI is required' }),
  redirectUri: z
    .string({ required_error: 'Redirect URI is required' })
    .min(1, { message: 'Redirect URI is required' }),
})

const CreateClientPage = () => {
  const navigate = useNavigate()
  const { mutateAsync, isPending } = useCreateClient()
  const toast = useCustomToast()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      clientName: '',
      description: '',
      clientUri: '',
      redirectUri: '',
    },
  })

  const submit = async (data: z.infer<typeof formSchema>) => {
    await mutateAsync(data, {
      onSuccess: () => {
        toast({ title: 'Client created' })
        navigate(ROUTE.CLIENTS)
      },
    })
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(submit)}>
          <Card className='w-full max-w-xl mx-auto'>
            <CardHeader>
              <CardTitle className='header'>Create Oauth Client</CardTitle>
            </CardHeader>
            <CardContent className='grid gap-4 text-left'>
              <FormField
                control={form.control}
                name='clientName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client Name</FormLabel>
                    <FormControl>
                      <Input placeholder='Client Name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      {/* <Input placeholder='Description' {...field} /> */}
                      <Textarea
                        placeholder='Description'
                        className='resize-none'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='clientUri'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client URI (Online Reservation URL)</FormLabel>
                    <FormControl>
                      <Input placeholder='URI' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='redirectUri'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client Redirect URI</FormLabel>
                    <FormControl>
                      <Input placeholder='Redirect URI' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <div className='w-full flex gap-4 lg:justify-end'>
                <Link to={ROUTE.CLIENTS} className='w-full lg:w-auto'>
                  <Button
                    type='submit'
                    className='w-full lg:w-auto'
                    variant='outline'
                  >
                    Cancel
                  </Button>
                </Link>
                <Button className='w-full lg:w-auto' disabled={isPending}>
                  {isPending && (
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  )}
                  Submit
                </Button>
              </div>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  )
}

export default CreateClientPage
