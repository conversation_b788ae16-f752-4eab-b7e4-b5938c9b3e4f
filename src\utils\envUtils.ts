interface IEnvConfigs {
    ClientId: string,
    PathBase: string,
    ThirdPartyApiUrl: string,
    IdentityApiUrl: string,
    Mode: string,
    RouteCacheEnabled: boolean
}

const ApiPathBases = {
    ThirdParty: 'thirdpartyapi',
    Identity: 'identityapi',
} as const;

function getEnvConfigs(): IEnvConfigs {
    const isDevelop: boolean = import.meta.env.DEV
    const routeCacheEnabled: boolean = import.meta.env.VITE_CACHE_ROUTE_ENABLED?.toLowerCase() === 'true'
    if (isDevelop || !routeCacheEnabled) {
        return {
            ClientId: import.meta.env.VITE_WEB_CLIENT_ID,
            PathBase: import.meta.env.VITE_WEB_PATH_BASE,
            ThirdPartyApiUrl: import.meta.env.VITE_API_URL,
            IdentityApiUrl: import.meta.env.VITE_AUTHORITY_BASE,
            Mode: import.meta.env.MODE,
            RouteCacheEnabled: routeCacheEnabled
        } as IEnvConfigs;
    }

    const origin = window.location.origin;
    return {
        ClientId: import.meta.env.VITE_WEB_CLIENT_ID,
        PathBase: import.meta.env.VITE_WEB_PATH_BASE,
        ThirdPartyApiUrl: `${origin}/${ApiPathBases.ThirdParty}`,
        IdentityApiUrl: `${origin}/${ApiPathBases.Identity}`,
        Mode: import.meta.env.MODE,
        RouteCacheEnabled: routeCacheEnabled
    } as IEnvConfigs;
}

export const envConfigs: IEnvConfigs = getEnvConfigs();
