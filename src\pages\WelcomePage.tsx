import { Loader2 } from 'lucide-react'
import { useState } from 'react'

import { But<PERSON> } from '@/components/ui/button'
import userManager from '@/utils/userManager'

const WelcomePage = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [errorMessage, setErrorMessage] = useState<string>('')

  const handleClickSignIn = () => {
    setIsLoading(true)
    setErrorMessage('')
    userManager
      .signinRedirect()
      .catch((error) => {
        setErrorMessage(error.message)
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  userManager.createSigninRequest()

  return (
    <div className='flex flex-col items-center h-full md:h-1/3 justify-center gap-1 text-center'>
      <h3 className='text-3xl md:text-4xl font-bold tracking-tight'>
        Welcome to OAuth Developer Registration Portal
      </h3>
      <p className='text-sm md:text-lg text-muted-foreground'>
        Please click sign in to continue
      </p>
      <Button
        className='mt-4 w-48'
        onClick={handleClickSignIn}
        disabled={isLoading}
      >
        {isLoading && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
        Sign In
      </Button>
      <p className='text-red-700 mt-1'>{errorMessage}</p>
    </div>
  )
}

export default WelcomePage
