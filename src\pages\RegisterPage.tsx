import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link } from 'react-router-dom'
import { ROUTE } from '../utils/route'
import { useRegisterClient } from '@/api/api'
import useCustomToast from '@/hooks/useCustomToast'
import userManager from '@/utils/userManager'
import { Loader2 } from 'lucide-react'
import {
  UPPER_CASE_REGEX,
  LOWER_CASE_REGEX,
  NUMBER_REGEX,
  SPECIAL_CHAR_REGEX,
  MIN_LENGTH_PASSWORD,
  INVALID_PASSWORD_COMPLEXITY_MSG,
} from '@/utils/constants/constants'

const formSchema = z
  .object({
    email: z
      .string({ required_error: 'Email is required' })
      .min(1, { message: 'Email is required' })
      .email({ message: 'Invalid email address' }),
    firstName: z
      .string({ required_error: 'FirstName is required' })
      .min(1, { message: 'FirstName is required' }),
    lastName: z
      .string({ required_error: 'Lastname is required' })
      .min(1, { message: 'Lastname is required' }),
    password: z
      .string({ required_error: 'Password is required' })
      .min(MIN_LENGTH_PASSWORD, {
        message: `Password length should be between ${MIN_LENGTH_PASSWORD} and 50 chars.`,
      })
      .max(50, {
        message: `Password length should be between ${MIN_LENGTH_PASSWORD} and 50 chars.`,
      }),
    confirmPassword: z
      .string({ required_error: 'Confirm Password is required' })
      .min(MIN_LENGTH_PASSWORD, {
        message: `Confirm Password length should be between ${MIN_LENGTH_PASSWORD} and 50 chars.`,
      })
      .max(50, {
        message: `Confirm Password length should be between ${MIN_LENGTH_PASSWORD} and 50 chars.`,
      }),
  })
  .refine(
    (data: { password: string }) => {
      const hasUpperCase = UPPER_CASE_REGEX.test(data.password)
      const hasLowerCase = LOWER_CASE_REGEX.test(data.password)
      const hasNumber = NUMBER_REGEX.test(data.password)
      const hasSymbol = SPECIAL_CHAR_REGEX.test(data.password)

      const complexityCount = [
        hasUpperCase,
        hasLowerCase,
        hasNumber,
        hasSymbol,
      ].filter(Boolean).length

      return complexityCount >= 3
    },
    {
      message: INVALID_PASSWORD_COMPLEXITY_MSG,
      path: ['password'],
    }
  )
  .refine(
    (data: { password: string; confirmPassword: string }) =>
      data.password === data.confirmPassword,
    {
      message: 'Passwords do not match',
      path: ['confirmPassword'],
    }
  )

const RegisterPage = () => {
  const toast = useCustomToast()
  const { mutateAsync, isPending } = useRegisterClient()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      firstName: '',
      lastName: '',
      password: '',
      confirmPassword: '',
    },
  })

  const submit = async (data: z.infer<typeof formSchema>) => {
    await mutateAsync(data, {
      onSuccess: () => {
        toast({ title: 'Registered successfully' })
        userManager.signinRedirect()
      },
    })
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(submit)}>
          <Card className='w-full max-w-xl mx-auto'>
            <CardHeader>
              <CardTitle className='header'>Register</CardTitle>
            </CardHeader>
            <CardContent className='grid gap-4 text-left'>
              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder='Email' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='firstName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder='First Name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='lastName'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder='Last Name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='password'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Password'
                        {...field}
                        type='password'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='confirmPassword'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Confirm Password'
                        {...field}
                        type='password'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <div className='w-full flex gap-4 lg:justify-end'>
                <Link to={ROUTE.WELCOME} className='w-full lg:w-auto'>
                  <Button
                    type='submit'
                    className='w-full lg:w-auto'
                    variant='outline'
                  >
                    Cancel
                  </Button>
                </Link>
                <Button className='w-full lg:w-auto' disabled={isPending}>
                  {isPending && (
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  )}
                  Submit
                </Button>
              </div>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  )
}

export default RegisterPage
