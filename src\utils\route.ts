interface RouteConfig {
  WELCOME: string
  LOGIN: string
  REGISTER: string
  CLIENTS: string
  CREATE_CLIENT: string
  EDIT_CLIENT: string
  CALLBACK: string
  EDIT_PROFILE: string
  RESET_PASSWORD: string
  FORGOT_PASSWORD: string
}

function getRoutePath(path: string): string {
  const pathBase: string = import.meta.env.VITE_WEB_PATH_BASE;
  if (!pathBase || pathBase === '/') {
    return path;
  }

  const result = `${pathBase}${path}`;
  return result;
}

export const ROUTE: RouteConfig = {
  WELCOME: getRoutePath('/'),
  CALLBACK: getRoutePath('/callback'),
  LOGIN: getRoutePath('/login'),
  REGISTER: getRoutePath('/register'),
  EDIT_PROFILE: getRoutePath('/profile/edit'),
  CLIENTS: getRoutePath('/clients'),
  CREATE_CLIENT: getRoutePath('/clients/create'),
  EDIT_CLIENT: getRoutePath('/clients/edit/:id'),
  RESET_PASSWORD: getRoutePath('/reset-password'),
  FORGOT_PASSWORD: getRoutePath('/forgot-password'),
}
