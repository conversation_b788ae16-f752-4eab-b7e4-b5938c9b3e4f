import React from 'react'
import {
  AlertD<PERSON>og,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from './ui/button'

interface ConfirmModalProps {
  openModal: boolean
  setOpenModal: React.Dispatch<React.SetStateAction<boolean>>
  body: string
  confirm: () => void
  cancel?: () => void
  isLoading?: boolean
}

const CustomAlertDialog = ({
  openModal,
  setOpenModal,
  body,
  confirm,
  cancel,
  isLoading = false,
}: ConfirmModalProps) => {
  function handleClose(event: React.MouseEvent<HTMLButtonElement>) {
    event.stopPropagation()

    if (isLoading) {
      return
    }

    setOpenModal(false)
    if (cancel) {
      cancel()
    }
  }

  return (
    <AlertDialog
      open={openModal}
      onOpenChange={(isOpen) => {
        if (isOpen === true) return
      }}
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Confirmation</AlertDialogTitle>
          <AlertDialogDescription>
            <div
              className='mb-5 text-lg'
              dangerouslySetInnerHTML={{ __html: body }}
            ></div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <div className='flex justify-center gap-2'>
            <Button variant='outline' onClick={(event) => handleClose(event)}>
              Cancel
            </Button>
            <Button
              type='button'
              variant='destructive'
              onClick={(event) => {
                event.stopPropagation()
                confirm()
              }}
              disabled={isLoading}
            >
              Confirm
            </Button>
          </div>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default CustomAlertDialog
