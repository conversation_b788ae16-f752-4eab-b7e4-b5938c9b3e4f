import { Link, useNavigate } from 'react-router-dom'
import { useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'

import { DataTable } from '@/components/DataTable'
import { ClientsColumns } from './ClientsColumns'
import { Button } from '@/components/ui/button'
import { ROUTE } from '@/utils/route'
import { useClients, useDeleteClient } from '@/api/api'
import useCustomToast from '@/hooks/useCustomToast'
import { PaginationResponse } from '@/types/PaginationResponse'
import { Client } from '@/types/Client'
import useDebounce from '@/hooks/useDebounce'
import CustomSearchInput from '@/components/CustomSearchInput'
import CustomSpinner from '@/components/CustomSpinner'

const ClientsPage = () => {
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const debounceSearchTerm = useDebounce(searchTerm)

  const { data, isLoading } = useClients({
    ...pagination,
    searchTerm: debounceSearchTerm,
  })
  const navigate = useNavigate()
  const toast = useCustomToast()
  const { isError, isSuccess, mutateAsync, isPending } = useDeleteClient()
  const queryClient = useQueryClient()

  const columns = ClientsColumns({
    bodyMessage: 'Are you sure you want to delete this client?',
    isDeleteError: isError,
    isDeleteSuccess: isSuccess,
    handleDeleteClient: async (id: number) => {
      await mutateAsync(id, {
        onSuccess: () => {
          toast({ title: 'Client deleted' })
          queryClient.invalidateQueries({ queryKey: ['clients'] })
        },
      })
    },
  })

  const handleRowClick = (client: Client) => {
    const route = ROUTE.EDIT_CLIENT.replace(':id', client.id.toString())
    navigate(route)
  }

  const handleSearchTextChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setSearchTerm(event.target.value)
  }

  if (isPending) {
    return <CustomSpinner label='Deleting client...' />
  }

  return (
    <div>
      <div className='header'>OAuth Client List</div>
      <div className='flex justify-between'>
        <CustomSearchInput onSearchTextChange={handleSearchTextChange} />
        <div>
          <Link to={ROUTE.CREATE_CLIENT}>
            <Button>Create new</Button>
          </Link>
        </div>
      </div>
      <div>
        <DataTable
          isLoading={isLoading}
          columns={columns}
          data={data as PaginationResponse<Client>}
          handleRowClick={handleRowClick}
          pagination={pagination}
          setPagination={setPagination}
        />
      </div>
    </div>
  )
}

export default ClientsPage
