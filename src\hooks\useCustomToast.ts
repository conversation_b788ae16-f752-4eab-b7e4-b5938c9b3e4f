import { toast } from 'sonner'

const useCustomToast = () => {
  const showToast = ({
    title,
    description,
  }: {
    title: string
    description?: string
  }) => {
    toast(title, {
      description: description || '',
      descriptionClassName: 'text-base',
      position: 'top-right',
      action: {
        label: 'Close',
        actionButtonStyle: {
          backgroundColor: 'red',
          color: 'white',
          width: '200px',
        },
        onClick: () => {},
      },
      classNames: {
        title: 'font-semibold text-lg ml-1',
        description: 'ml-1',
        toast: 'border-2 rounded-lg bg-white p-4',
      },
    })
  }

  return showToast
}

export default useCustomToast
