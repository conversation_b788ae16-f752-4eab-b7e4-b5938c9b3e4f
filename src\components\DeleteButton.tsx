import { useEffect, useState } from 'react'
import CustomButton from './CustomButton'
import { Trash2 } from 'lucide-react'
import CustomAlertDialog from './CustomAlertDialog'

interface DeleteButtonProps {
  body?: string
  confirm: () => void
  isRequestCompleted?: boolean
}

const DeleteButton = ({
  body = 'Are you surre to delete this data?',
  confirm,
  isRequestCompleted = false,
}: DeleteButtonProps) => {
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isOpenDeleteConfirmModal, setIsOpenDeleteConfirmModal] =
    useState(false)

  useEffect(() => {
    if (isRequestCompleted) {
      setIsOpenDeleteConfirmModal(false)
      setIsLoading(false)
    }
  }, [isRequestCompleted])

  const handleCancelDeleteEmployee = () => {
    setIsOpenDeleteConfirmModal(false)
  }

  const handleClickConfirm = () => {
    setIsLoading(true)
    confirm()
  }

  return (
    <>
      <CustomAlertDialog
        body={body}
        openModal={isOpenDeleteConfirmModal}
        setOpenModal={setIsOpenDeleteConfirmModal}
        confirm={handleClickConfirm}
        cancel={handleCancelDeleteEmployee}
        isLoading={isLoading}
      />
      <CustomButton
        variant='ghost'
        size='xsmall'
        onClick={(event) => {
          event.stopPropagation()
          setIsOpenDeleteConfirmModal(true)
        }}
      >
        <Trash2 className='w-4 h-4 text-red-700' />
      </CustomButton>
    </>
  )
}

export default DeleteButton
