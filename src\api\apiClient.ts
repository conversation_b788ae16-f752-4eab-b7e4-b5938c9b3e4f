import { ComponentLabel } from '@/enums/ComponentLabel'
import { MessageError } from '@/types/MessageError'
import { envConfigs } from '@/utils/envUtils'
import { getAPIErrors } from '@/utils/errorUtils'
import userManager from '@/utils/userManager'
import { getUserToken } from '@/utils/userUtils'
import axios, { AxiosError } from 'axios'
import { toast } from 'sonner'

const apiClient = axios.create({
  baseURL: envConfigs.ThirdPartyApiUrl,
  headers: {
    'Content-type': 'application/json',
    'X-ApiKey': import.meta.env.VITE_API_KEY,
    'client-id': envConfigs.ClientId,
    'x-componentid': ComponentLabel.ThirdpartyAPIs,
  },
})

// Add a request interceptor
apiClient.interceptors.request.use(
  (config) => {
    const token = getUserToken()

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Add a response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response.status === 401) {
      userManager.signoutRedirect()
      localStorage.clear()
    }

    showErrorToast(error)

    return Promise.reject(error)
  }
)

const showErrorToast = (error: AxiosError<MessageError>) => {
  const errorDescription = getAPIErrors(error)
  toast(`Something went wrong.`, {
    description: errorDescription,
    duration: 8000,
    descriptionClassName: 'text-base',
    position: 'top-right',
    action: {
      label: 'Close',
      actionButtonStyle: {
        backgroundColor: 'red',
        color: 'white',
        width: '200px',
      },
      onClick: () => { },
    },
    classNames: {
      title: `font-semibold text-lg ml-1 text-red-700`,
      description: 'ml-1',
      toast: 'border-2 rounded-lg bg-white p-4',
    },
  })
}

export default apiClient
