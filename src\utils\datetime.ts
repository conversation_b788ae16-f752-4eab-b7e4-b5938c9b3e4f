const pad = (number: number): string =>
  number < 10 ? `0${number}` : `${number}`

export const formatDate = (date: Date): string => {
  const year = date.getFullYear()
  const month = pad(date.getMonth() + 1)
  const day = pad(date.getDate())

  let hours = date.getHours()
  const minutes = pad(date.getMinutes())

  const ampm = hours >= 12 ? 'PM' : 'AM'
  hours = hours % 12
  hours = hours ? hours : 12 // the hour '0' should be '12'
  const formattedHours = pad(hours)

  return `${year}-${month}-${day} ${formattedHours}:${minutes} ${ampm}`
}
