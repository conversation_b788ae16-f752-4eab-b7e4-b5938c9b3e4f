import { Loader2 } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { <PERSON> } from 'react-router-dom'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from '@/components/ui/card'
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  Form,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ROUTE } from '@/utils/route'
import { useForgotPassword } from '@/api/api'
import useCustomToast from '@/hooks/useCustomToast'

const formSchema = z.object({
  email: z
    .string({ required_error: 'Email is required' })
    .min(1, { message: 'Email is required' })
    .email({
      message: 'Invalid email address',
    }),
})

const ForgotPasswordPage = () => {
  const { mutateAsync, isPending } = useForgotPassword()
  const toast = useCustomToast()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  })

  const submit = async (data: z.infer<typeof formSchema>) => {
    await mutateAsync(
      {
        ...data,
        resetPasswordUri: `${window.location.origin}${ROUTE.RESET_PASSWORD}`,
      },
      {
        onSuccess: () =>
          toast({
            title: 'Password Recovery',
            description:
              'Your Login information will be sent via email in a few minutes. Please check your email for login instructions and try again.',
          }),
      }
    )
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(submit)}>
          <Card className='w-full max-w-sm mx-auto'>
            <CardHeader>
              <CardTitle className='header'>Reset Password</CardTitle>
            </CardHeader>
            <CardContent className='grid gap-4 text-left'>
              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder='Email' {...field} type='email' />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <div className='w-full flex gap-4 lg:justify-end'>
                <Link to={ROUTE.WELCOME} className='w-full lg:w-auto'>
                  <Button
                    type='submit'
                    className='w-full lg:w-auto'
                    variant='outline'
                  >
                    Cancel
                  </Button>
                </Link>
                <Button className='w-full lg:w-auto' disabled={isPending}>
                  {isPending && (
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  )}
                  Submit
                </Button>
              </div>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  )
}

export default ForgotPasswordPage
