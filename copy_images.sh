#!/usr/bin/env bash

# http://redsymbol.net/articles/unofficial-bash-strict-mode/
set -euo pipefail

usage() {
  cat <<END
copy_images.sh: copy docker images to staging environment.
Parameters:
  -t | --tag <docker image tag>
    Specifies API docker image version
  -t-opt | --tag-onlineoptionsweb <docker image tag>
    Specify onlineoptionsweb docker image version
  -t-res | --tag-onlineresweb <docker image tag>
    Specify onlineresweb docker image version
  -t-oauthreg | --tag-oauthregistrationportalweb <docker image tag>
    Specify oauthregistrationportalweb docker image version
  --no-cache
    Disable cache when pulling images(default: false)
  -h | --help
    Displays this help text and exits the script

How to call script example:

./copy_images.sh -t latest -t-opt latest -t-res latest -t-oauthreg latest
./copy_images.sh -t latest -t-opt latest -t-res latest -t-oauthreg latest --no-cache
./copy_images.sh -t v4-3.108.9033.14735 -t-opt v4-3.108.9035.8481 -t-res v4-3.108.9026.12012 -t-oauthreg stable
./copy_images.sh -t v4-3.108.9033.14735 -t-opt v4-3.108.9035.8481 -t-res v4-3.108.9026.12012 -t-oauthreg stable --no-cache

END
}

#region functions

pullSourceImages() {
  local images=("${@:1:$#-1}")
  local tag="${@: -1}"

  for image in "${images[@]}"; do
    echo " - Pulling $SOURCE_REPO_BASE/$image:$tag image..."

    if [[ $no_cache ]]; then
      docker rmi $SOURCE_REPO_BASE/$image:$tag -f
    fi

    docker pull $SOURCE_REPO_BASE/$image:$tag
  done
}

pushDestinationImages() {
  local images=("${@:1:$#-2}")
  local sourceTag="${@: -2:1}"
  local destTag="${@: -1}"

  for image in "${images[@]}"; do
    echo " - Taging and pushing $DEST_REPO_BASE/$image:$destTag image to registry..."
    docker tag $SOURCE_REPO_BASE/$image:$sourceTag $DEST_REPO_BASE/$image:$destTag
    docker push $DEST_REPO_BASE/$image:$destTag
  done
}

validateInvalidData() {
  local isValid=""

  # Check required data source
  if [[ -f $docker_password_file ]] && [[ "$(cat $docker_password_file | tr -d '[:space:]' 2>/dev/null)" ]] || [[ $docker_access_Token ]] || [[ $docker_password ]]; then
    isValid="true"
  fi

  if [[ ! $isValid ]]; then
    echo "Invalid Docker login credentials source."
    return
  fi

  # Check required data destination
  if [[ -f $docker_password_file_dest ]] && [[ "$(cat $docker_password_file_dest | tr -d '[:space:]' 2>/dev/null)" ]] || [[ $docker_access_Token_dest ]] || [[ $docker_password_dest ]]; then
    isValid="true"
  fi

  if [[ ! $isValid ]]; then
    echo "Invalid Docker login credentials destination."
    return
  fi

  echo ""
}

pullImages() {
  if [[ $docker_access_Token ]]; then
    echo "login docker with credentials source by access token."
    echo ${docker_access_Token} | docker login -u oauth2accesstoken --password-stdin $REPO_URL
  elif [[ $docker_password ]]; then
    echo "login docker with credentials source by provided data."
    echo ${docker_password} | docker login -u _json_key --password-stdin $REPO_URL
  elif [[ -f $docker_password_file ]] && [[ "$(cat $docker_password_file | tr -d '[:space:]' 2>/dev/null)" ]]; then
    echo "login docker with credentials source by '${docker_password_file}' file."
    docker login -u _json_key --password-stdin $REPO_URL <$docker_password_file
  else
    echo "Invalid Docker login credentials source."
    return
  fi

  echo""
  if [[ $api_version ]]; then
    pullSourceImages "${API_IMAGES[@]}" $api_version
  fi

  if [[ $onlineoptionsweb_version ]]; then
    pullSourceImages "${OPTIONWEB_IMAGE[@]}" $onlineoptionsweb_version
  fi

  if [[ $onlineresweb_version ]]; then
    pullSourceImages "${ORESWEB_IMAGE[@]}" $onlineresweb_version
  fi

  if [[ $oauthreg_version ]]; then
    pullSourceImages "${OAUTHREG_IMAGE[@]}" $oauthreg_version
  fi

  docker logout $REPO_URL
}

pushImages() {
  if [[ $docker_access_Token_dest ]]; then
    echo "login docker with credentials source by access token."
    echo ${docker_access_Token_dest} | docker login -u oauth2accesstoken --password-stdin $REPO_URL
  elif [[ $docker_password_dest ]]; then
    echo "login docker with credentials destination data."
    echo ${docker_password_dest} | docker login -u _json_key --password-stdin $REPO_URL
  elif [[ -f $docker_password_file_dest ]] && [[ "$(cat $docker_password_file_dest | tr -d '[:space:]' 2>/dev/null)" ]]; then
    echo "login docker with credentials destination '${docker_password_file_dest}' file."
    docker login -u _json_key --password-stdin $REPO_URL <$docker_password_file_dest
  else
    echo "Invalid Docker login credentials destination."
    return
  fi

  echo""
  if [[ $api_version ]]; then
    pushDestinationImages "${API_IMAGES[@]}" $api_version $api_version
    processResults+=("Current API version: ${api_version} is copied!")
  fi

  if [[ $onlineoptionsweb_version ]]; then
    pushDestinationImages "${OPTIONWEB_IMAGE[@]}" $onlineoptionsweb_version $onlineoptionsweb_version
    processResults+=("Current Online Options Web version: ${onlineoptionsweb_version} is copied!")
  fi

  if [[ $onlineresweb_version ]]; then
    pushDestinationImages "${ORESWEB_IMAGE[@]}" $onlineresweb_version $onlineresweb_version
    processResults+=("Current Online Reservation Web version: ${onlineresweb_version} is copied!")
  fi

  if [[ $oauthreg_version ]]; then
    pushDestinationImages "${OAUTHREG_IMAGE[@]}" $oauthreg_version $oauthreg_version
    processResults+=("Current OAuth Registration Portal Web version: ${oauthreg_version} is copied!")
  fi

  docker logout $REPO_URL
}

copyImages() {
  pullImages
  echo ""
  pushImages
}

displayCurrentValues() {
  echo "----- Current Values: -----"
  echo "api_version = ${api_version}"
  echo "onlineoptionsweb_version = ${onlineoptionsweb_version}"
  echo "onlineresweb_version = ${onlineresweb_version}"
  echo "oauthreg_version = ${oauthreg_version}"
  echo "no_cache = ${no_cache}"
}

displayResults() {
  echo "----- Result: -----"
  if [ ${#processResults[@]} -gt 0 ]; then
    for resultTmp in "${processResults[@]}"; do
      echo $resultTmp
    done
  else
    echo "No images have been copied!"
  fi
}

#endregion functions

REPO_URL=https://gcr.io
# SOURCE_REPO_BASE="gcr.io/cpsv4-225808"
# DEST_REPO_BASE="gcr.io/cpsv4-staging"
SOURCE_REPO_BASE="gcr.io/cpsv4-staging"
DEST_REPO_BASE="gcr.io/cpsv4-225808"

API_IMAGES=(cpsv4saleapi cpsv4identityapi cpsv4inventoryapi cpsv4onlinereservationapi cpsv4shapi cpsv4customerapi cpsv4emailapi cpsv4referencesapi cpsv4employeeapi cpsv4webstatus cpsv4backgroundtasks cpsv4thirdpartyapi cpsv4airapi)
OPTIONWEB_IMAGE=(cpsv4onlineoptionsweb)
ORESWEB_IMAGE=(cpsv4onlinereservationweb)
OAUTHREG_IMAGE=(cpsv4oauthregistrationportalweb)

api_version=""
onlineoptionsweb_version=""
onlineresweb_version=""
oauthreg_version=""
no_cache=""
# docker_password=${DOCKER_PASSWORD-}
# docker_password_dest=${DOCKER_PASSWORD_STAGING-}
# docker_access_Token=${GOOGLE_OAUTH_ACCESS_TOKEN-}
# docker_password_file="deploy/k8s/helm/secrets/gcr-key.json"
# docker_password_file_dest="deploy/k8s/helm/secrets/gcr-key-staging.json"
docker_password=${DOCKER_PASSWORD_STAGING-}
docker_password_dest=${DOCKER_PASSWORD-}
docker_access_Token=${GOOGLE_ACCESS_TOKEN_STAGING-}
docker_access_Token_dest=${GOOGLE_ACCESS_TOKEN-}
docker_password_file="deploy/k8s/helm/secrets/gcr-key-staging.json"
docker_password_file_dest="deploy/k8s/helm/secrets/gcr-key.json"
processResults=()

while [[ $# -gt 0 ]]; do
  case "$1" in
  -t | --tag)
    api_version="$2"
    shift 2
    ;;
  -t-opt | --tag-onlineoptionsweb)
    onlineoptionsweb_version="$2"
    shift 2
    ;;
  -t-res | --tag-onlineresweb)
    onlineresweb_version="$2"
    shift 2
    ;;
  -t-oauthreg | --tag-oauthreg)
    oauthreg_version="$2"
    shift 2
    ;;
  --no-cache)
    no_cache="yes"
    shift
    ;;
  -h | --help)
    usage
    exit 1
    ;;
  *)
    echo "Unknown option $1"
    usage
    exit 2
    ;;
  esac
done

# Check input parameters
isValidInputs=false

if [[ $api_version ]]; then
  isValidInputs=true
fi

if [[ $onlineoptionsweb_version ]]; then
  isValidInputs=true
fi

if [[ $onlineresweb_version ]]; then
  isValidInputs=true
fi

if [[ $oauthreg_version ]]; then
  isValidInputs=true
fi

if [[ $isValidInputs == false ]]; then
  echo "* (APIs, Online Options Web, Online Reservation Web, OAuth Registration Portal Web) image tag version must be specified (e.g. 4.0.0, latest)"
  echo ""
  usage
  exit 3
fi

SECONDS=0

displayCurrentValues

validateResult=$(validateInvalidData)
if [[ $validateResult ]]; then
  echo $validateResult
  exit
fi

echo "Start copying docker images..."
copyImages
echo ""

displayResults

echo "All Done!!!"
echo "Elapsed: $(($SECONDS / 3600))hrs $((($SECONDS / 60) % 60))min $(($SECONDS % 60))sec"
echo ""
