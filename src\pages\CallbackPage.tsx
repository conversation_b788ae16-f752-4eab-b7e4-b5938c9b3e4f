import { useEffect } from 'react'

import { ROUTE } from '@/utils/route'
import userManager from '@/utils/userManager'
import CustomSpinner from '@/components/CustomSpinner'

const CallbackPage = () => {
  useEffect(() => {
    userManager
      .signinCallback()
      .then((user) => {
        if (user) {
          localStorage.setItem('user', JSON.stringify(user))
          // setAuthHeader(user?.access_token)
          window.location.href = ROUTE.CLIENTS
        }
      })
      .catch(function (err) {
        console.error(err)
      })
  }, [])

  return <CustomSpinner />
}

export default CallbackPage
