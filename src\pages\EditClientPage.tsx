import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link, useNavigate, useParams } from 'react-router-dom'
import { ROUTE } from '../utils/route'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { useClientDetail, useUpdateClient } from '@/api/api'
import { useEffect } from 'react'
import { formatDate } from '@/utils/datetime'
import { Loader2 } from 'lucide-react'
import useCustomToast from '@/hooks/useCustomToast'

const formSchema = z.object({
  clientName: z
    .string({ required_error: 'Name is required' })
    .min(1, { message: 'Name is required' }),
  description: z
    .string({ required_error: 'Description is required' })
    .min(1, { message: 'Description is required' }),
  clientUri: z
    .string({ required_error: 'URI is required' })
    .min(1, { message: 'URI is required' }),
  redirectUri: z
    .string({ required_error: 'Redirect URI is required' })
    .min(1, { message: 'Redirect URI is required' }),
})

type EditClientPageParams = {
  id: string
}

const EditClientPage: React.FC = () => {
  const { id } = useParams<EditClientPageParams>()
  const { data: client } = useClientDetail(id as string)
  const { mutateAsync, isPending } = useUpdateClient()
  const toast = useCustomToast()
  const navigate = useNavigate()

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      clientName: '',
      description: '',
      clientUri: '',
      redirectUri: '',
    },
  })

  useEffect(() => {
    if (client) {
      form.reset({
        clientName: client.clientName,
        description: client.description,
        clientUri: client.clientUri,
        redirectUri: client.clientRedirectUri.redirectUri,
      })
    }
  }, [client, form])

  const submit = async (data: z.infer<typeof formSchema>) => {
    await mutateAsync(
      { client: { ...data, id: client?.id } },
      {
        onSuccess: () => {
          toast({ title: 'Client updated' })
          navigate(ROUTE.CLIENTS)
        },
      }
    )
  }

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(submit)}>
          <Card className='w-full max-w-4xl mx-auto'>
            <CardHeader>
              <CardTitle className='header'>Edit Oauth Client</CardTitle>
            </CardHeader>
            <CardContent className='grid md:grid-cols-2 gap-4 text-left'>
              <div className='space-y-2'>
                <FormItem>
                  <FormControl>
                    <div className='flex items-center space-x-2'>
                      <Checkbox
                        id='enabled'
                        checked={client?.enabled}
                        disabled
                      />
                      <FormLabel htmlFor='enabled'>Enabled</FormLabel>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
                <FormItem>
                  <FormLabel>Client Id</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Client Id'
                      value={client?.clientId || ''}
                      disabled
                      showCopy={client?.clientId ? true : false}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
                <FormItem>
                  <FormLabel>Client Secret</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Client Secret'
                      value={client?.clientSecret || ''}
                      disabled
                      showCopy={client?.clientSecret ? true : false}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
                <FormField
                  control={form.control}
                  name='clientName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client Name</FormLabel>
                      <FormControl>
                        <Input placeholder='Client Name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        {/* <Input placeholder='Description' {...field} /> */}
                        <Textarea
                          placeholder='Description'
                          className='resize-none'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className='space-y-2'>
                <FormField
                  control={form.control}
                  name='clientUri'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client URI (Online Reservation URL)</FormLabel>
                      <FormControl>
                        <Input placeholder='URI' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className='flex gap-2 items-center'>
                  <FormField
                    control={form.control}
                    name='redirectUri'
                    render={({ field }) => (
                      <FormItem className='w-full'>
                        <FormLabel>Client Redirect URI</FormLabel>
                        <FormControl>
                          <Input placeholder='Redirect URI' {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormItem className='mt-8'>
                    <FormControl>
                      <div className='flex items-center space-x-2'>
                        <Checkbox
                          id='enableClientRedirect'
                          checked={client?.clientRedirectUri?.enabled}
                          disabled
                        />
                        <FormLabel htmlFor='enableClientRedirect'>
                          Enabled
                        </FormLabel>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </div>
                <FormItem>
                  <FormLabel>Created Date</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Created Date'
                      disabled
                      value={
                        client?.createdOnUtcDate
                          ? formatDate(new Date(client?.createdOnUtcDate))
                          : ''
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
                <FormItem>
                  <FormLabel>Updated Date</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Updated Date'
                      disabled
                      value={
                        client?.updatedOnUtcDate
                          ? formatDate(new Date(client?.updatedOnUtcDate))
                          : ''
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </div>
            </CardContent>
            <CardFooter>
              <div className='w-full flex gap-4 lg:justify-end'>
                <Link to={ROUTE.CLIENTS} className='w-full lg:w-auto'>
                  <Button
                    type='submit'
                    className='w-full lg:w-auto'
                    variant='outline'
                  >
                    Cancel
                  </Button>
                </Link>
                <Button className='w-full lg:w-auto' disabled={isPending}>
                  {isPending && (
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  )}
                  Save
                </Button>
              </div>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  )
}

export default EditClientPage
