#!/bin/sh

# APP_DIR="/usr/share/nginx/html"
APP_DIR="/app/dist"
ENV_PREFIX="MY_APP_"

for i in $(env | grep $ENV_PREFIX)
do
	key=$(echo $i | cut -d '=' -f 1)
	value=$(echo $i | cut -d '=' -f 2-)
	echo $key=$value
    # sed All files
	# find /usr/share/nginx/html -type f -exec sed -i "s|${key}|${value}|g" '{}' +

    # sed JS and CSS only
    find $APP_DIR -type f \( -name '*.js' -o -name '*.css' \) -exec sed -i "s|${key}|${value}|g" '{}' +
done