ARG VERSION=20-alpine
FROM node:$VERSION as base

# Set timezone and locale
ENV TZ=America/New_York \
    LANG=en_US.UTF-8 \
    LANGUAGE=en_US:en \
    LC_ALL=en_US.UTF-8
RUN apk update \
    && apk add --no-cache \
    musl musl-utils musl-locales tzdata
RUN cp /usr/share/zoneinfo/$TZ /etc/localtime
RUN echo "export LC_ALL=${LC_ALL}" >> /etc/profile.d/locale.sh && \
    sed -i "s|LANG=C.UTF-8|LANG=${LANG}|" /etc/profile.d/locale.sh

FROM node:$VERSION AS builder
WORKDIR /app

COPY package*.json ./

RUN npm install --force
COPY . .
RUN npm run build

# Production environment
FROM base AS final
WORKDIR /app
COPY --from=builder /app/dist/ /app/dist/

COPY package*.json ./
COPY vite.config.ts ./

RUN npm install typescript --legacy-peer-deps

COPY env.sh ./docker-entrypoint.d/env.sh
RUN chmod +x ./docker-entrypoint.d/env.sh

EXPOSE 8080

ENTRYPOINT sh -c "./docker-entrypoint.d/env.sh && npm run preview"
