import * as React from "react"

import { cn } from "@/lib/utils"
import { Copy } from 'lucide-react'
import useCustomToast from "@/hooks/useCustomToast";

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  showCopy?: boolean;
}

const onCopyToClipboard = (text: string) => {
  const toast = useCustomToast()

  try {
    navigator.clipboard.writeText(text);
    toast({ title: 'Copy to clipboard successful' });
  } catch {
    toast({ title: 'The browser does not support copy to clipboard' });
  }
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, showCopy, ...props }, ref) => {
    return (
      <div className="flex items-center h-10 w-full rounded-md border">
        <input
          type={type}
          className={cn("w-full rounded-md border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50", className, showCopy ? "pr-1" : "")}
          ref={ref}
          {...props}
        />
        {showCopy &&
          <div className="p-1 pr-2">
            <div
              title="Copy"
              onClick={() => onCopyToClipboard(props.value?.toString() || '')}
              className="cursor-pointer rounded-sm bg-blue-500">
              <Copy className="p-[5px]" color="white" />
            </div>
          </div>
        }
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input }
