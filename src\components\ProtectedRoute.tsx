import { getUserData } from '@/utils/userUtils'
import { ROUTE } from '@/utils/route'
import { Navigate } from 'react-router-dom'

interface ProtectedRouteProps {
  children: React.ReactNode
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const userData = getUserData()

  if (!userData) {
    return <Navigate to={ROUTE.WELCOME} replace />
  }

  return children
}

export default ProtectedRoute
