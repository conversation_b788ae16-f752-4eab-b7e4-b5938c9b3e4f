import { useMutation, useQuery } from '@tanstack/react-query'

import apiClinet from './apiClient'
import { PaginationResponse } from '@/types/PaginationResponse'
import { Client } from '@/types/Client'

const useClients = (pagination: {
  pageIndex: number
  pageSize: number
  searchTerm: string
}) => {
  return useQuery({
    queryKey: ['clients', pagination],
    queryFn: async () => {
      const response = await apiClinet.get<PaginationResponse<Client>>(
        'api/ThirdPartyUsers/OAuthClients',
        {
          params: {
            CurrentPage: pagination.pageIndex + 1,
            PageSize: pagination.pageSize,
            SearchTerm: pagination.searchTerm ?? '',
          },
        }
      )
      return response.data
    },
  })
}

const useClientDetail = (id: string) => {
  return useQuery({
    queryKey: ['clientDetail', id],
    queryFn: async () => {
      const response = apiClinet.get<Client>(
        `api/ThirdPartyUsers/OAuthClientDetail/${id}`
      )
      return (await response).data
    },
  })
}

const useCreateClient = () => {
  return useMutation({
    mutationFn: async (client: Partial<Client>) => {
      return await apiClinet.post(
        `api/ThirdPartyUsers/OAuthClient`,
        client
      )
    },
  })
}

const useUpdateClient = () => {
  return useMutation({
    mutationFn: async ({ client }: { client: Partial<Client> }) => {
      return await apiClinet.put(
        `api/ThirdPartyUsers/OAuthClient`,
        client
      )
    },
  })
}

const useDeleteClient = () => {
  return useMutation({
    mutationFn: async (id: number) => {
      return await apiClinet.delete(
        `api/ThirdPartyUsers/OAuthClient/${id}`
      )
    },
  })
}

const useLoginClient = () => {
  return useMutation({
    mutationFn: async (login: { email: string; password: string }) => {
      return await apiClinet.post(
        'api/ThirdPartyUsers/Login',
        login
      )
    },
  })
}

const useRegisterClient = () => {
  return useMutation({
    mutationFn: async (register: {
      email: string
      firstName: string
      lastName: string
      password: string
      confirmPassword: string
    }) => {
      return await apiClinet.post(
        'api/ThirdPartyUsers/Registration',
        register
      )
    },
  })
}

const useUpdatePassword = () => {
  return useMutation({
    mutationFn: async (data: { newPassword: string; key: string, email: string, timeStamp: string }) => {
      return await apiClinet.put(
        `api/ThirdPartyUsers/Password`,
        data
      )
    },
  })
}

const useForgotPassword = () => {
  return useMutation({
    mutationFn: async (data: { email: string; resetPasswordUri: string }) => {
      return await apiClinet.post(
        'api/ThirdPartyUsers/ForgotPassword',
        data
      )
    },
  })
}

const useUpdateProfile = () => {
  return useMutation({
    mutationFn: async (data: {
      firstName: string
      lastName: string
      email: string
    }) => {
      return await apiClinet.put(`api/ThirdPartyUsers`, data)
    },
  })
}

export {
  useClients,
  useClientDetail,
  useCreateClient,
  useUpdateClient,
  useDeleteClient,
  useLoginClient,
  useRegisterClient,
  useUpdatePassword,
  useUpdateProfile,
  useForgotPassword,
}
