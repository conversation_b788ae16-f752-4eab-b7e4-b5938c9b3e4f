import { cn } from '@/lib/utils'

const CustomSpinner = ({ className, label = 'Loading...' }: { className?: string, label?: string }) => {
  return (
    <div className='w-full h-full flex items-center flex-col justify-center'>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='24'
        height='24'
        viewBox='0 0 24 24'
        fill='none'
        stroke='currentColor'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
        className={cn('animate-spin', className)}
      >
        <path d='M21 12a9 9 0 1 1-6.219-8.56' />
      </svg>
      <div className='ml-2'>{label}</div>
    </div>
  )
}

export default CustomSpinner
