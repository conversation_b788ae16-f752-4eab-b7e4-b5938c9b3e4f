import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { CircleUser, CircleUserRound, Earth, LogOut } from 'lucide-react'
import { Link, useNavigate } from 'react-router-dom'
import { ROUTE } from '@/utils/route'
import { getUserData } from '@/utils/userUtils'
import userManager from '@/utils/userManager'

const Header = () => {
  const navigate = useNavigate()
  const userData = getUserData()

  const handleLogout = () => {
    userManager.signoutRedirect()
    localStorage.clear()
    navigate(ROUTE.WELCOME)
  }

  return (
    <header className='sticky top-0 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6 z-10'>
      <div className='flex w-full justify-between items-center gap-4 md:ml-auto md:gap-2 lg:gap-4'>
        <Link to={ROUTE.CLIENTS}>
          <h1 className='font-semibold text-2xl'>
            OAuth Developer Registration Portal
          </h1>
        </Link>
        {userData && (
          <div className='flex gap-2 items-center'>
            <h1 className='hidden sm:!block text-xl font-semibold'>
              {userData?.profile?.email}
            </h1>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant='secondary'
                  size='icon'
                  className='rounded-full'
                >
                  <CircleUser className='h-5 w-5' />
                  <span className='sr-only'>Toggle user menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuItem onClick={() => navigate(ROUTE.EDIT_PROFILE)}>
                  <CircleUserRound className='mr-2' />
                  Profile
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate(ROUTE.CLIENTS)}>
                  <Earth className='mr-2' />
                  Manage OAuth Cliens
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className='mr-2' />
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
