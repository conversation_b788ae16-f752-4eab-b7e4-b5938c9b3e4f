# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/pipeline/#customization
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Container Scanning customization: https://docs.gitlab.com/ee/user/application_security/container_scanning/#customizing-the-container-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence

image: docker:stable

services:
- docker:dind

stages:
- test
- check-security
- build
- tag-version
- deploy

variables:
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ''
  DOCKER_REGISTRY: "${DOCKER_REGISTRY_URL}/"
  DOCKER_REGISTRY_STAGING: "${DOCKER_REGISTRY_URL_STAGING}/"
  REGISTRY_HOST_URL: https://gcr.io
  OIDC_AUDIENCE: https://gitdev.clubprophetsystems.com
  OIDC_AUDIENCE_STAGING: https://gitdev.clubprophetsystems.com

# Include GitLab security templates
include:
  - template: Security/SAST.gitlab-ci.yml
  - template: Security/Dependency-Scanning.gitlab-ci.yml

# Custom job initialization (reused across jobs)
.job_init_docker: &job_init_docker
  before_script:
    # Install required packages
    - apk add --update --no-cache curl py-pip bash jq
    - pip install docker-compose~=1.23.0
    # Export required envs
    - export DOCKER_REGISTRY_URL=$DOCKER_REGISTRY_URL_STAGING
    - export DOCKER_REGISTRY=$DOCKER_REGISTRY_STAGING
    # Get gcp tokens
    - chmod +x ./deploy/k8s/get-gcp-sts-staging.sh && . ./deploy/k8s/get-gcp-sts-staging.sh
    - echo ${GOOGLE_ACCESS_TOKEN_STAGING} | docker login -u oauth2accesstoken --password-stdin $REGISTRY_HOST_URL
    - export DOCKER_PASSWORD="$(echo ${DOCKER_PASSWORD_ENC} | base64 -d)"
    - export DOCKER_PASSWORD_STAGING="$(echo ${DOCKER_PASSWORD_ENC_STAGING} | base64 -d)"

# SAST Job
sast:
  stage: check-security
  variables:
    SEARCH_MAX_DEPTH: '20'
    SAST_EXCLUDED_PATHS: public,obj,bin,node_modules,**/.node-version,**/.editorconfig,**/.dockerignore,**/.gitignore

# Dependency Scanning Job
gemnasium-dependency_scanning:
  stage: check-security
  artifacts:
    reports:
      dependency_scanning: gl-dependency-scanning-report.json

# Build Job
build:
  stage: build
  tags:
  - v4dockercloud
  id_tokens:
    GITLAB_OIDC_TOKEN_STAGING:
      aud: $OIDC_AUDIENCE_STAGING
    GITLAB_OIDC_TOKEN:
      aud: $OIDC_AUDIENCE
  <<: *job_init_docker
  script:
  - echo "Building image..."
  # Get gcp tokens from prod env
  - chmod +x ./deploy/k8s/get-gcp-sts.sh && . ./deploy/k8s/get-gcp-sts.sh
  - chmod +x ./deploy/k8s/build_images.sh && ./deploy/k8s/build_images.sh
  after_script:
  - docker logout $REGISTRY_HOST_URL
  only:
  - main
  - "/^[0-9]+(?:.[0-9]+)+$/"
  - fix/MI-8-change-default-cicd-to-staging-env

# Tag Version Job
tag_version:
  stage: tag-version
  tags:
  - v4dockercloud
  id_tokens:
    GITLAB_OIDC_TOKEN_STAGING:
      aud: $OIDC_AUDIENCE_STAGING
    GITLAB_OIDC_TOKEN:
      aud: $OIDC_AUDIENCE
  <<: *job_init_docker
  script:
  - echo "Tagging image version..."
  # Get gcp tokens from prod env
  - chmod +x ./deploy/k8s/get-gcp-sts.sh && . ./deploy/k8s/get-gcp-sts.sh
  - chmod +x ./deploy/k8s/tag_images.sh && ./deploy/k8s/tag_images.sh
  after_script:
  - docker logout $REGISTRY_HOST_URL
  when: manual
  only:
  - main
  - "/^[0-9]+(?:.[0-9]+)+$/"
  - fix/MI-8-change-default-cicd-to-staging-env
