#!/usr/bin/env bash

# http://redsymbol.net/articles/unofficial-bash-strict-mode
set -euo pipefail

source .env

images=$IMAGES

for image in $images; do
 echo " - Pulling ${DOCKER_REGISTRY}${image}:${TAG} image."
 docker rmi ${DOCKER_REGISTRY}${image}:${TAG} -f
 docker pull ${DOCKER_REGISTRY}${image}:${TAG}
done

for image in $images; do
 echo " - Tagging and pushing ${DOCKER_REGISTRY}${image}:${TAG_STABLE} image to docker registry."
 docker tag ${DOCKER_REGISTRY}${image}:${TAG} ${DOCKER_REGISTRY}${image}:${TAG_STABLE}
 docker push ${DOCKER_REGISTRY}${image}:${TAG_STABLE}
done

# Copy images to staging env
chmod +x copy_images.sh
./copy_images.sh -t-oauthreg $TAG_STABLE
