import { User } from 'oidc-react'

export const getUserData: () => User | null = () => {
  const jsonString = localStorage.getItem('user')
  let userData: User | null = null

  if (jsonString) {
    try {
      userData = JSON.parse(jsonString)
    } catch (error) {
      console.error('Error parsing JSON:', error)
    }
  }

  return userData
}

export const updateUserData = (
  first_name: string,
  last_name: string,
  email: string
) => {
  const user = getUserData()
  if (user) {
    user.profile.first_name = first_name
    user.profile.last_name = last_name
    user.profile.email = email
    localStorage.setItem('user', JSON.stringify(user))
  }
}

export const getUserToken: () => string | undefined = () => {
  const user = getUserData()
  return user?.access_token
}

export const getUserId: () => string | undefined = () => {
  const user = getUserData()
  return user?.profile?.sub
}
