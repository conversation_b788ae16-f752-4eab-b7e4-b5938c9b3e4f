import { ColumnDef } from '@tanstack/react-table'

import DeleteButton from '@/components/DeleteButton'
import { Client } from '@/types/Client'
import { formatDate } from '@/utils/datetime'

interface ClientsColumnsProps {
  bodyMessage: string
  isDeleteError: boolean
  isDeleteSuccess: boolean
  handleDeleteClient: (id: number) => void
}

export const ClientsColumns = ({
  bodyMessage,
  isDeleteError,
  isDeleteSuccess,
  handleDeleteClient,
}: ClientsColumnsProps): ColumnDef<Client>[] => [
  {
    accessorKey: 'clientId',
    header: 'Client Id',
  },
  {
    accessorKey: 'clientName',
    header: 'Client Name',
  },
  {
    accessorKey: 'enabled',
    header: 'Enabled',
    cell: ({ row }) => (
      <div
        className={`font-medium ${
          row.original.enabled ? 'text-green-700' : 'text-red-700'
        }`}
      >
        {row.original.enabled ? 'True' : 'False'}
      </div>
    ),
  },
  {
    accessorKey: 'createdOnUtcDate',
    header: 'Created Date',
    cell: ({ row }) => {
      return formatDate(new Date(row.original.createdOnUtcDate))
    },
  },
  {
    accessorKey: 'actions',
    header: '',
    cell: ({ row }) => (
      <div className='text-right'>
        <DeleteButton
          isRequestCompleted={isDeleteSuccess || isDeleteError}
          body={bodyMessage}
          confirm={() => handleDeleteClient(row.original.id)}
        />
      </div>
    ),
  },
]
