#!/bin/sh -x
# NOTE: Install missing package
# apt-get install -y jq

# NOTE: Configure GitLab variables: ${GCP_IDENTITY_POOLS_PROVIDER} ${GCP_CLOUD_BUILD_SA}

PAYLOAD="$(cat <<EOF
{
  "audience": "//iam.googleapis.com/${GCP_IDENTITY_POOLS_PROVIDER_STAGING}",
  "grantType": "urn:ietf:params:oauth:grant-type:token-exchange",
  "requestedTokenType": "urn:ietf:params:oauth:token-type:access_token",
  "scope": "https://www.googleapis.com/auth/cloud-platform",
  "subjectTokenType": "urn:ietf:params:oauth:token-type:jwt",
  "subjectToken": "${GITLAB_OIDC_TOKEN_STAGING}"
}
EOF
)"

FEDERATED_TOKEN="$(curl --fail "https://sts.googleapis.com/v1/token" \
  --header "Accept: application/json" \
  --header "Content-Type: application/json" \
  --data "${PAYLOAD}" \
  | jq -r '.access_token'
)"

ACCESS_TOKEN="$(curl --fail "https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/${GCP_CLOUD_BUILD_SA_STAGING}:generateAccessToken" \
  --header "Accept: application/json" \
  --header "Content-Type: application/json" \
  --header "Authorization: Bearer ${FEDERATED_TOKEN}" \
  --data '{"scope": ["https://www.googleapis.com/auth/cloud-platform"]}' \
  | jq -r '.accessToken'
)"
# echo "${ACCESS_TOKEN}"

# Export to Google environment variables for using in gcloud cli
# export CLOUDSDK_AUTH_ACCESS_TOKEN=$ACCESS_TOKEN
export GOOGLE_ACCESS_TOKEN_STAGING=$ACCESS_TOKEN

echo "GOOGLE_ACCESS_TOKEN_STAGING = ${GOOGLE_ACCESS_TOKEN_STAGING}"
