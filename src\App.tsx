import { lazy, Suspense } from 'react'
import { BrowserRouter, Route, Routes } from 'react-router-dom'
import { ErrorBoundary } from 'react-error-boundary'

import Layout from './components/Layout'
import { ROUTE } from './utils/route'
import { QueryClient, QueryClientContext } from '@tanstack/react-query'
import ProtectedRoute from './components/ProtectedRoute'
import CustomSpinner from './components/CustomSpinner'
import { envConfigs } from './utils/envUtils'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
})

const WelcomePage = lazy(() => import('./pages/WelcomePage'))
const CallbackPage = lazy(() => import('./pages/CallbackPage'))
const LoginPage = lazy(() => import('./pages/LoginPage'))
const RegisterPage = lazy(() => import('./pages/RegisterPage'))
const ClientsPage = lazy(() => import('./pages/ClientsPage/ClientsPage'))
const CreateClientPage = lazy(() => import('./pages/CreateClientPage'))
const EditClientPage = lazy(() => import('./pages/EditClientPage'))
const EditProfilePage = lazy(() => import('./pages/EditProfilePage'))
const ErrorPage = lazy(() => import('./pages/ErrorPage'))
const ResetPasswordPage = lazy(() => import('./pages/ResetPasswordPage'))
const ForgotPasswordPage = lazy(() => import('./pages/ForgotPasswordPage'))

const routes = [
  { path: ROUTE.CLIENTS, element: <ClientsPage /> },
  { path: ROUTE.CREATE_CLIENT, element: <CreateClientPage /> },
  { path: ROUTE.EDIT_CLIENT, element: <EditClientPage /> },
  { path: ROUTE.EDIT_PROFILE, element: <EditProfilePage /> },
]

function App() {
  const envValues = JSON.stringify(envConfigs);
  console.info('Env values:\n' + envValues);

  return (
    <>
      <QueryClientContext.Provider value={queryClient}>
        <BrowserRouter>
          <ErrorBoundary fallback={<ErrorPage />}>
            <Suspense fallback={<CustomSpinner />}>
              <Routes>
                <Route index path={ROUTE.WELCOME} element={<WelcomePage />} />
                <Route path={ROUTE.CALLBACK} element={<CallbackPage />} />
                <Route path='/' element={<Layout />}>
                  <Route path={ROUTE.LOGIN} element={<LoginPage />} />
                  <Route path={ROUTE.REGISTER} element={<RegisterPage />} />
                  <Route
                    path={ROUTE.FORGOT_PASSWORD}
                    element={<ForgotPasswordPage />}
                  />
                  <Route
                    path={ROUTE.RESET_PASSWORD}
                    element={<ResetPasswordPage />}
                  />

                  {routes.map((route) => (
                    <Route
                      key={route.path}
                      path={route.path}
                      element={<ProtectedRoute>{route.element}</ProtectedRoute>}
                    />
                  ))}
                </Route>
              </Routes>
            </Suspense>
          </ErrorBoundary>
        </BrowserRouter>
      </QueryClientContext.Provider>
    </>
  )
}

export default App
